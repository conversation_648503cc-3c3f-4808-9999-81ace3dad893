%%% -*-BibTeX-*-
%%% Do NOT edit. File created by BibTeX with style
%%% ACM-Reference-Format-Journals [18-Jan-2012].

\begin{thebibliography}{5}

%%% ====================================================================
%%% NOTE TO THE USER: you can override these defaults by providing
%%% customized versions of any of these macros before the \bibliography
%%% command.  Each of them MUST provide its own final punctuation,
%%% except for \shownote{} and \showURL{}.  The latter two
%%% do not use final punctuation, in order to avoid confusing it with
%%% the Web address.
%%%
%%% To suppress output of a particular field, define its macro to expand
%%% to an empty string, or better, \unskip, like this:
%%%
%%% \newcommand{\showURL}[1]{\unskip}   % LaTeX syntax
%%%
%%% \def \showURL #1{\unskip}           % plain TeX syntax
%%%
%%% ====================================================================

\ifx \showCODEN    \undefined \def \showCODEN     #1{\unskip}     \fi
\ifx \showISBNx    \undefined \def \showISBNx     #1{\unskip}     \fi
\ifx \showISBNxiii \undefined \def \showISBNxiii  #1{\unskip}     \fi
\ifx \showISSN     \undefined \def \showISSN      #1{\unskip}     \fi
\ifx \showLCCN     \undefined \def \showLCCN      #1{\unskip}     \fi
\ifx \shownote     \undefined \def \shownote      #1{#1}          \fi
\ifx \showarticletitle \undefined \def \showarticletitle #1{#1}   \fi
\ifx \showURL      \undefined \def \showURL       {\relax}        \fi
% The following commands are used for tagged output and should be
% invisible to TeX
\providecommand\bibfield[2]{#2}
\providecommand\bibinfo[2]{#2}
\providecommand\natexlab[1]{#1}
\providecommand\showeprint[2][]{arXiv:#2}

\bibitem[{OWASP Foundation}(2024)]%
        {zap2024}
\bibfield{author}{\bibinfo{person}{{OWASP Foundation}}.}
  \bibinfo{year}{2024}\natexlab{}.
\newblock \bibinfo{title}{OWASP ZAP: The World's Most Widely Used Web App
  Scanner}.
\newblock
\urldef\tempurl%
\url{https://www.zaproxy.org/}
\showURL{%
\tempurl}
\newblock
\shownote{Accessed: 2024-08-03}.


\bibitem[{Pallets Projects}(2024)]%
        {flask2024}
\bibfield{author}{\bibinfo{person}{{Pallets Projects}}.}
  \bibinfo{year}{2024}\natexlab{}.
\newblock \bibinfo{title}{Flask: A Lightweight WSGI Web Application Framework}.
\newblock
\urldef\tempurl%
\url{https://flask.palletsprojects.com/}
\showURL{%
\tempurl}
\newblock
\shownote{Accessed: 2024-08-04}.


\bibitem[{PyCQA}(2024)]%
        {bandit2024}
\bibfield{author}{\bibinfo{person}{{PyCQA}}.} \bibinfo{year}{2024}\natexlab{}.
\newblock \bibinfo{title}{Bandit: A Security Linter for Python}.
\newblock
\urldef\tempurl%
\url{https://github.com/PyCQA/bandit}
\showURL{%
\tempurl}
\newblock
\shownote{Python Code Quality Authority. Accessed: 2024-08-03}.


\bibitem[Shahriar and Zulkernine(2021)]%
        {shahriar2021comprehensive}
\bibfield{author}{\bibinfo{person}{Hossain Shahriar} {and}
  \bibinfo{person}{Mohammad Zulkernine}.} \bibinfo{year}{2021}\natexlab{}.
\newblock \showarticletitle{A comprehensive survey of web application security
  testing}.
\newblock \bibinfo{journal}{\emph{Software Quality Journal}}
  \bibinfo{volume}{29}, \bibinfo{number}{2} (\bibinfo{year}{2021}),
  \bibinfo{pages}{351--386}.
\newblock
\href{https://doi.org/10.1007/s11219-020-09530-2}{doi:\nolinkurl{10.1007/s11219-020-09530-2}}


\bibitem[Vieira et~al\mbox{.}(2009)]%
        {vieira2009comparing}
\bibfield{author}{\bibinfo{person}{Marco Vieira}, \bibinfo{person}{Nuno
  Antunes}, {and} \bibinfo{person}{Henrique Madeira}.}
  \bibinfo{year}{2009}\natexlab{}.
\newblock \showarticletitle{Comparing Web Application Security Scanners}.
\newblock \bibinfo{journal}{\emph{IEEE Security \& Privacy}}
  \bibinfo{volume}{7}, \bibinfo{number}{3} (\bibinfo{year}{2009}),
  \bibinfo{pages}{52--60}.
\newblock
\href{https://doi.org/10.1109/MSP.2009.75}{doi:\nolinkurl{10.1109/MSP.2009.75}}


\end{thebibliography}

@misc{owasp2021,
  title={OWASP Top 10:2021 - The Ten Most Critical Web Application Security Risks},
  author={{OWASP Foundation}},
  year={2021},
  url={https://owasp.org/Top10/},
  note={Accessed: 2024-08-03}
}

@misc{flask2024,
  title={Flask: A Lightweight WSGI Web Application Framework},
  author={{Pallets Projects}},
  year={2024},
  url={https://flask.palletsprojects.com/},
  note={Accessed: 2024-08-04}
}

@article{vieira2009comparing,
  title={Comparing Web Application Security Scanners},
  author={<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and Madeira, Henrique},
  journal={IEEE Security \& Privacy},
  volume={7},
  number={3},
  pages={52--60},
  year={2009},
  publisher={IEEE},
  doi={10.1109/MSP.2009.75}
}

@article{alqahtani2022systematic,
  title={A Systematic Literature Review on the Characteristics and Effectiveness of Web Application Vulnerability Scanners},
  author={<PERSON><PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON>},
  journal={IEEE Access},
  volume={10},
  pages={32270--32288},
  year={2022},
  publisher={IEEE},
  doi={10.1109/ACCESS.2022.3161362}
}

@misc{bandit2024,
  title={Bandit: A Security Linter for Python},
  author={{PyCQA}},
  year={2024},
  url={https://github.com/PyCQA/bandit},
  note={Python Code Quality Authority. Accessed: 2024-08-03}
}

@misc{zap2024,
  title={OWASP ZAP: The World's Most Widely Used Web App Scanner},
  author={{OWASP Foundation}},
  year={2024},
  url={https://www.zaproxy.org/},
  note={Accessed: 2024-08-03}
}

@article{soni2022prevention,
  title={Prevention of SQL injection attack by using black box testing},
  author={Soni, Priyanka and others},
  booktitle={Proceedings of the 23rd International Conference on Distributed Computing and Networking},
  pages={266--271},
  year={2022},
  organization={ACM},
  doi={10.1145/3491003.3491026}
}

@article{almomani2022access,
  title={Automating the Detection of Access Control Vulnerabilities in Web Applications},
  author={Almomani, Iman M and Alqahtani, Sultan S},
  journal={SN Computer Science},
  volume={3},
  number={4},
  pages={1--16},
  year={2022},
  publisher={Springer},
  doi={10.1007/s42979-022-01271-1}
}

@book{grinberg2018flask,
  title={Flask Web Development: Developing Web Applications with Python},
  author={Grinberg, Miguel},
  year={2018},
  publisher={O'Reilly Media},
  edition={2nd},
  isbn={978-1491991732}
}

@article{shahriar2021comprehensive,
  title={A comprehensive survey of web application security testing},
  author={Shahriar, Hossain and Zulkernine, Mohammad},
  journal={Software Quality Journal},
  volume={29},
  number={2},
  pages={351--386},
  year={2021},
  publisher={Springer},
  doi={10.1007/s11219-020-09530-2}
}

@inproceedings{doupé2010johnny,
  title={Why Johnny can't pentest: An analysis of black-box web vulnerability scanners},
  author={Doupé, Adam and Cova, Marco and Vigna, Giovanni},
  booktitle={International conference on detection of intrusions and malware, and vulnerability assessment},
  pages={111--131},
  year={2010},
  organization={Springer},
  doi={10.1007/978-3-642-14215-4_7}
}

@misc{pytest2024,
  title={pytest: helps you write better programs},
  author={{pytest Development Team}},
  year={2024},
  url={https://pytest.org/},
  note={Accessed: 2024-08-03}
}

@inproceedings{halfond2006classification,
  title={A classification of SQL-injection attacks and countermeasures},
  author={Halfond, William GJ and Viegas, Jeremy and Orso, Alessandro},
  booktitle={Proceedings of the IEEE international symposium on secure software engineering},
  volume={1},
  pages={13--15},
  year={2006},
  organization={IEEE}
}

@article{hydara2015current,
  title={Current state of research on cross-site scripting (XSS)--a systematic literature review},
  author={Hydara, Isatou and Sultan, Abu Bakar Md and Zulzalil, Hazura and Admodisastro, Novia},
  journal={Information and software technology},
  volume={58},
  pages={170--186},
  year={2015},
  publisher={Elsevier},
  doi={10.1016/j.infsof.2014.06.010}
}
